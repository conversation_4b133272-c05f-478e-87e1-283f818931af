NODE_ENV=
NEXT_PUBLIC_APP_URL=

# Database for Docker (local)
DATABASE_DB_NAME=
DATABASE_USER=
DATABASE_PASSWORD=
DATABASE_HOST=
DATABASE_PORT=

# Database URL for connection to the database
DATABASE_URL=

# Azure OpenAI
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_KEY=

# Cloudflare R2
CLOUDFLARE_ACCOUNT_ID=
CLOUDFLARE_ACCESS_KEY_ID=
CLOUDFLARE_SECRET_ACCESS_KEY=
CLOUDFLARE_BUCKET_NAME=

# Email (Resend)
RESEND_API_KEY=
FROM_EMAIL=