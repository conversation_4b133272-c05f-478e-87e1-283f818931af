name: Database Migration

on:
  push:
    branches:
      - main
  release:
    types:
      - published

jobs:
  migrate:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      # - name: Install dependencies
      #   run: npm install

      - name: Install drizzle dependencies
        run: npm install drizzle-kit drizzle-orm

      - name: Run database migrations
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: npm run db:migrate
