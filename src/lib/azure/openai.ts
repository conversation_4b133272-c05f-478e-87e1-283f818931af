import { ChatInteraction } from '@/hooks/use-chat';
import { env } from '@/lib/env.mjs';
import { createAzure } from '@ai-sdk/azure';
import { AzureOpenAI } from 'openai';

export const azure = createAzure({
  resourceName: env.AZURE_OPENAI_RESOURCE_NAME,
  apiKey: env.AZURE_OPENAI_API_KEY,
  apiVersion: '2024-04-01-preview',
  baseURL: 'https://medical-center.cognitiveservices.azure.com/openai/deployments/',
});

const client = new AzureOpenAI({
  endpoint: env.AZURE_OPENAI_ENDPOINT,
  apiVersion: '2024-04-01-preview',
  apiKey: env.AZURE_OPENAI_API_KEY,
});

export async function getModelList(): Promise<string[]> {
  const result = await client.models.list();
  return result.data.map((item: { id: string }) => item.id);
}

/**
 * Generate embeddings for a list of texts in batches to handle large documents efficiently
 * @param texts Array of text strings to embed
 * @param batchSize Number of texts to process in each batch (default: 400)
 * @param debug Whether to log detailed processing information (default: false)
 * @returns Array of embedding vectors
 */
export async function generateEmbeddings(
  texts: string[],
  batchSize = 400,
  debug = false
): Promise<number[][]> {
  const embeddingClient = new AzureOpenAI({
    endpoint: env.AZURE_OPENAI_ENDPOINT,
    apiVersion: '2024-04-01-preview',
    apiKey: env.AZURE_OPENAI_API_KEY,
    deployment: 'text-embedding-3-small-global',
  });

  // If texts array is smaller than batch size, process in one go
  if (texts.length <= batchSize) {
    const result = await embeddingClient.embeddings.create({
      input: texts,
      model: 'text-embedding-3-small',
      dimensions: 1536,
    });
    return result.data.map((item: { embedding: number[] }) => item.embedding);
  }

  // For large documents, process in batches with full parallelization
  const totalBatches = Math.ceil(texts.length / batchSize);
  if (debug) {
    console.log(
      `Processing ${texts.length} embeddings in ${totalBatches} batches of ${batchSize} items each`
    );
    console.log(`Running all batches in parallel (no concurrency limit)`);
  }

  // Prepare all batches
  const batches: string[][] = [];
  for (let i = 0; i < totalBatches; i++) {
    const batchStart = i * batchSize;
    const batchEnd = Math.min((i + 1) * batchSize, texts.length);
    batches.push(texts.slice(batchStart, batchEnd));
  }

  // Process batches with controlled parallelism
  const embeddings: number[][] = new Array(texts.length);
  const startTime = performance.now();

  // Process batches with limited concurrency using a simple semaphore pattern
  const processBatchWithRetry = async (batchIndex: number): Promise<void> => {
    const batchTexts = batches[batchIndex];
    const batchStart = batchIndex * batchSize;
    const batchStartTime = performance.now();

    if (debug) {
      console.log(
        `Starting batch ${batchIndex + 1}/${totalBatches} (${batchTexts.length} items)...`
      );
    }

    try {
      const result = await embeddingClient.embeddings.create({
        input: batchTexts,
        model: 'text-embedding-3-small',
        dimensions: 1536,
      });

      // Place the embeddings in the correct positions in the final array
      result.data.forEach((item, i) => {
        embeddings[batchStart + i] = item.embedding;
      });

      if (debug) {
        const batchEndTime = performance.now();
        console.log(
          `✅ Batch ${batchIndex + 1}/${totalBatches} completed in ${((batchEndTime - batchStartTime) / 1000).toFixed(2)}s`
        );
      }
    } catch (error) {
      console.error(`❌ Error processing batch ${batchIndex + 1}:`, error);

      // If a batch fails, process each item individually
      if (debug) {
        console.log(`Attempting to process batch ${batchIndex + 1} items individually...`);
      }

      for (let j = 0; j < batchTexts.length; j++) {
        const itemIndex = batchStart + j;
        try {
          const itemResult = await embeddingClient.embeddings.create({
            input: [batchTexts[j]],
            model: 'text-embedding-3-small',
            dimensions: 1536,
          });

          embeddings[itemIndex] = itemResult.data[0].embedding;
          if (debug) {
            console.log(`  ✓ Item ${itemIndex + 1}/${texts.length} processed successfully`);
          }
        } catch (itemError) {
          console.error(`  ✗ Failed to process item ${itemIndex + 1}:`, itemError);
          // Insert a zero vector as a placeholder for failed items
          embeddings[itemIndex] = new Array(1536).fill(0);
        }
      }
    }
  };

  // Process all batches in parallel without limiting concurrency
  const batchPromises = batches.map((_, index) => processBatchWithRetry(index));
  await Promise.all(batchPromises);

  if (debug) {
    const endTime = performance.now();
    const totalTime = (endTime - startTime) / 1000;
    console.log(`✅ All ${texts.length} embeddings processed in ${totalTime.toFixed(2)}s`);
    console.log(`Average throughput: ${(texts.length / totalTime).toFixed(2)} embeddings/second`);
  }

  return embeddings;
}

export async function generateChatResponse(
  messages: ChatInteraction[],
  options: { temperature?: number; max_tokens?: number } = { temperature: 0.7, max_tokens: 100 }
) {
  const transformMessages = messages.map((msg) => ({
    role:
      msg.senderType === 'user'
        ? 'user'
        : msg.senderType === 'system'
          ? 'system'
          : ('assistant' as 'user' | 'assistant' | 'system'),
    content: msg.content,
  }));

  const result = await client.chat.completions.create({
    model: 'gpt-4o-mini',
    messages: transformMessages,
    temperature: options.temperature,
    stream: true,
    max_tokens: options.max_tokens,
  });

  return result;
}
