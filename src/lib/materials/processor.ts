import { generateEmbeddings } from '@/lib/azure/openai';
import { r2Client } from '@/lib/cloudflare/r2';
import { env } from '@/lib/env.mjs';
import { GetObjectCommand } from '@aws-sdk/client-s3';
import { DocxLoader } from '@langchain/community/document_loaders/fs/docx';
import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
import { Document } from '@langchain/core/documents';
import { TextLoader } from 'langchain/document_loaders/fs/text';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { generateDocumentSummary } from './summarizer';

/**
 * Process a material file and generate embeddings for its content
 * @param file File to process
 * @param fileType MIME type of the file
 * @param debug Whether to log detailed processing information (default: false)
 * @returns Processed chunks with embeddings
 */
export async function processMaterial(file: File, fileType: string, debug = false) {
  if (debug) {
    console.log(`\n==== STARTING MATERIAL PROCESSING ====`);
    console.log(
      `File type: ${fileType}, File name: ${file.name}, Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`
    );
  }
  const processStartTime = performance.now();

  // Declare timing variables at function scope
  let loadStartTime = 0;
  let loadEndTime = 0;
  let splitStartTime = 0;
  let splitEndTime = 0;
  let embeddingStartTime = 0;
  let embeddingEndTime = 0;
  let combineStartTime = 0;
  let combineEndTime = 0;

  const supportedTypes = [
    'text/plain',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
  ];

  if (!supportedTypes.includes(fileType)) {
    console.error(`Unsupported file type: ${fileType}`);
    throw new Error(
      `Unsupported file type: ${fileType}. Supported types are: ${supportedTypes.join(', ')}`
    );
  }

  let loader;
  switch (fileType) {
    case 'application/pdf':
      loader = new PDFLoader(file);
      break;
    case 'application/msword':
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      loader = new DocxLoader(file);
      break;
    case 'text/plain':
      loader = new TextLoader(file);
      break;
    default:
      throw new Error(`Unsupported file type: ${fileType}`);
  }

  if (debug) {
    console.log(`Loading file content using ${loader.constructor.name}...`);
  }
  loadStartTime = performance.now();

  let pages;
  try {
    pages = await loader.load();
    loadEndTime = performance.now();
    if (debug) {
      console.log(
        `✅ File loaded successfully in ${((loadEndTime - loadStartTime) / 1000).toFixed(2)}s`
      );
      console.log(`Total pages/documents: ${pages.length}`);
      if (pages.length > 0) {
        console.log(`First page content length: ${pages[0].pageContent.length} chars`);
      }
    }
  } catch (error) {
    console.error('❌ Error loading file:', error);
    throw error;
  }

  // Split text into chunks
  if (debug) {
    console.log('Splitting content into chunks...');
  }
  splitStartTime = performance.now();

  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize: 1000,
    chunkOverlap: 200,
  });

  let chunks;
  try {
    chunks = await splitter.splitDocuments(pages);
    splitEndTime = performance.now();
    if (debug) {
      console.log(
        `✅ Content split into ${chunks.length} chunks in ${((splitEndTime - splitStartTime) / 1000).toFixed(2)}s`
      );
      console.log(`Chunk size config: ${splitter.chunkSize}, overlap: ${splitter.chunkOverlap}`);
    }
  } catch (error) {
    console.error('❌ Error splitting content:', error);
    throw error;
  }

  if (debug) {
    console.log('Generating embeddings...');
  }
  embeddingStartTime = performance.now();

  let embeddings;
  try {
    // Calculate optimal batch size based on chunk count
    const totalChunks = chunks.length;

    // Default to larger batch sizes to reduce API calls (6000 per minute limit)
    let batchSize = 400;

    // For smaller documents, process in a single batch
    if (totalChunks <= 400) {
      batchSize = totalChunks;
    }
    // For large documents, use a batch size of 300
    else if (totalChunks > 1000) {
      batchSize = 300;
    }

    if (debug) {
      console.log(`Using batch size of ${batchSize} for ${totalChunks} total chunks`);
    }

    // Extract chunk contents
    const chunkContents = chunks.map((chunk) => chunk.pageContent);

    // Generate embeddings with batching and full parallelization
    embeddings = await generateEmbeddings(chunkContents, batchSize, debug);

    embeddingEndTime = performance.now();
    const embeddingTime = (embeddingEndTime - embeddingStartTime) / 1000;
    if (debug) {
      console.log(`✅ Embeddings generated in ${embeddingTime.toFixed(2)}s`);
      console.log(
        `Generated ${embeddings.length} embeddings (${(embeddings.length / embeddingTime).toFixed(2)} embeddings/second)`
      );
      if (embeddings.length > 0) {
        console.log(`First embedding dimensions: ${embeddings[0].length}`);
      }
    }
  } catch (error) {
    console.error('❌ Error generating embeddings:', error);
    throw error;
  }

  // Combine chunks with embeddings
  if (debug) {
    console.log('Combining chunks with embeddings...');
  }
  combineStartTime = performance.now();

  const chunksWithEmbeddings = chunks.map((chunk: Document, index: number) => {
    return {
      content: chunk.pageContent,
      chunkOrder: index,
      metadata: chunk.metadata?.loc || {},
      embedding: embeddings[index],
    };
  });

  combineEndTime = performance.now();
  if (debug) {
    console.log(
      `✅ Chunks combined with embeddings in ${((combineEndTime - combineStartTime) / 1000).toFixed(2)}s`
    );
  }

  const processEndTime = performance.now();
  const totalProcessingTime = (processEndTime - processStartTime) / 1000;
  if (debug) {
    console.log(`\n==== MATERIAL PROCESSING COMPLETE ====`);
    console.log(`Total processing time: ${totalProcessingTime.toFixed(2)}s`);
    console.log(`Breakdown: 
      - File loading: ${(((loadEndTime - loadStartTime) / 1000 / totalProcessingTime) * 100).toFixed(2)}%
      - Content splitting: ${(((splitEndTime - splitStartTime) / 1000 / totalProcessingTime) * 100).toFixed(2)}%
      - Embedding generation: ${(((embeddingEndTime - embeddingStartTime) / 1000 / totalProcessingTime) * 100).toFixed(2)}%
      - Combining results: ${(((combineEndTime - combineStartTime) / 1000 / totalProcessingTime) * 100).toFixed(2)}%
    `);
  }

  // Generate document summary for dual-layer retrieval
  if (debug) {
    console.log('Generating document summary...');
  }
  const summaryStartTime = performance.now();

  let summaryData = null;
  try {
    summaryData = await generateDocumentSummary(
      chunksWithEmbeddings.map((chunk) => ({
        content: chunk.content,
        chunkOrder: chunk.chunkOrder,
      })),
      file.name,
      debug
    );

    const summaryEndTime = performance.now();
    if (debug) {
      console.log(
        `✅ Enhanced document summary generated in ${((summaryEndTime - summaryStartTime) / 1000).toFixed(2)}s`
      );
      console.log(`Summary length: ${summaryData.summary.length} characters`);
      console.log(`Key topics: ${summaryData.keyTopics.join(', ')}`);
      console.log(`Document type: ${summaryData.documentType}`);
    }
  } catch (error) {
    console.error('❌ Error generating document summary:', error);
    // Continue without summary - this is not a critical failure
    if (debug) {
      console.log('⚠️ Continuing without document summary');
    }
  }

  const finalProcessEndTime = performance.now();
  const finalTotalTime = (finalProcessEndTime - processStartTime) / 1000;
  if (debug && summaryData) {
    console.log(`Updated total processing time: ${finalTotalTime.toFixed(2)}s`);
  }

  return {
    chunks: chunksWithEmbeddings,
    summary: summaryData,
  };
}

/**
 * Process a material file from Cloudflare R2 storage
 * @param fileKey Key of the file in R2 storage
 * @param fileName Original filename for the document
 * @param fileType MIME type of the file
 * @param debug Whether to log detailed processing information (default: false)
 * @returns Processed chunks with embeddings
 */
export async function processMaterialFromR2(
  fileKey: string,
  fileName: string,
  fileType: string,
  debug = false
) {
  if (debug) {
    console.log(`\n==== STARTING R2 MATERIAL PROCESSING ====`);
    console.log(`File key: ${fileKey}, File type: ${fileType}`);
  }
  const r2ProcessStartTime = performance.now();

  const supportedTypes = [
    'text/plain',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
  ];

  if (!supportedTypes.includes(fileType)) {
    console.error(`Unsupported file type: ${fileType}`);
    throw new Error(
      `Unsupported file type: ${fileType}. Supported types are: ${supportedTypes.join(', ')}`
    );
  }

  try {
    // Download file from R2
    if (debug) {
      console.log(`Downloading file from R2 bucket: ${env.CLOUDFLARE_BUCKET_NAME}...`);
    }
    const downloadStartTime = performance.now();

    const getObjectCommand = new GetObjectCommand({
      Bucket: env.CLOUDFLARE_BUCKET_NAME,
      Key: fileKey,
    });

    let response;
    try {
      response = await r2Client.send(getObjectCommand);
      if (debug) {
        const downloadEndTime = performance.now();
        console.log(
          `✅ File downloaded from R2 in ${((downloadEndTime - downloadStartTime) / 1000).toFixed(2)}s`
        );
      }
    } catch (error) {
      console.error('❌ Error downloading file from R2:', error);
      throw error;
    }

    if (!response.Body) {
      console.error('Response body is empty');
      throw new Error('Failed to retrieve file from R2');
    }

    // Convert the stream to an array buffer
    if (debug) {
      console.log('Converting stream to byte array...');
    }
    const streamConvertStartTime = performance.now();

    let arrayBuffer;
    try {
      arrayBuffer = await response.Body.transformToByteArray();
      if (debug) {
        const streamConvertEndTime = performance.now();
        console.log(
          `✅ Stream converted to byte array in ${((streamConvertEndTime - streamConvertStartTime) / 1000).toFixed(2)}s`
        );
        console.log(`File size: ${(arrayBuffer.length / 1024 / 1024).toFixed(2)} MB`);
      }
    } catch (error) {
      console.error('❌ Error converting stream to byte array:', error);
      throw error;
    }

    // Create a File object from the array buffer using the actual filename
    const file = new File([arrayBuffer], fileName, { type: fileType });
    if (debug) {
      console.log(
        `File object created: ${file.name}, Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`
      );
      console.log('Passing file to processMaterial function...');
    }

    const r2ProcessEndTime = performance.now();
    if (debug) {
      console.log(
        `R2 preparation time: ${((r2ProcessEndTime - r2ProcessStartTime) / 1000).toFixed(2)}s`
      );
    }

    // Pass the debug flag to the processMaterial function
    return processMaterial(file, fileType, debug);
  } catch (error) {
    console.error('Error processing material from R2:', error);
    throw new Error('Failed to process material from R2');
  }
}
