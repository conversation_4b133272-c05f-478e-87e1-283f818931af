import type { RetrievedDocumentChunkDTO } from '@/types/chat';
import type { MaterialSearchResult } from '@/types/material';
import { Annotation } from '@langchain/langgraph';

// Document summary type for dual-layer retrieval
export interface DocumentSummary {
  id: string;
  fileName: string;
  summary: string;
  similarity?: number;
  key: string;
  fileType: string;
}

// Simplified state following LangGraph best practices
export const AgentState = Annotation.Root({
  // Original user query (never changes)
  originalQuery: Annotation<string>,

  // Current processed query (can be refined)
  currentQuery: Annotation<string>,

  // Array of refined keywords for search
  refinedKeywords: Annotation<string[]>({
    reducer: (current, update) => update,
    default: () => [],
  }),

  // Reasoning from AI analysis steps
  reasoning: Annotation<string>({
    reducer: (current, update) => update,
    default: () => '',
  }),

  // Retrieved document chunks
  retrievedChunks: Annotation<MaterialSearchResult[]>({
    reducer: (current, update) => update, // Replace instead of concat to avoid duplication
    default: () => [],
  }),

  // Retrieved document summaries
  retrievedSummaries: Annotation<DocumentSummary[]>({
    reducer: (current, update) => update, // Replace instead of concat
    default: () => [],
  }),

  // Query intent classification
  queryIntent: Annotation<'research' | 'casual' | 'greeting' | 'clarification'>,

  // Whether to skip research and respond directly
  skipResearch: Annotation<boolean>,

  // Evaluation result for routing decisions
  evaluationResult: Annotation<'sufficient' | 'need_summary' | 'need_refinement' | 'failed'>,

  // Retrieval strategy determined during analysis
  retrievalStrategy: Annotation<
    'chunks_only' | 'summaries_only' | 'both_chunks_and_summaries' | null
  >({
    reducer: (current, update) => update,
    default: () => 'chunks_only',
  }),

  // Target materials for retrieval
  targetMaterials: Annotation<Array<{ id: string; fileName: string; reason: string }>>({
    reducer: (current, update) => update,
    default: () => [],
  }),

  // Material selection reasoning
  materialSelectionReasoning: Annotation<string>({
    reducer: (current, update) => update,
    default: () => '',
  }),

  // Chat history relevance (optional enhancement)
  needsHistory: Annotation<boolean>({
    reducer: (current, update) => update,
    default: () => false,
  }),

  historyReasoning: Annotation<string>({
    reducer: (current, update) => update,
    default: () => '',
  }),

  chatHistory: Annotation<string>({
    reducer: (current, update) => update,
    default: () => '',
  }),

  // Research plan properties
  planType: Annotation<
    'direct_summary' | 'keyword_search' | 'comprehensive_research' | 'document_not_found'
  >({
    reducer: (current, update) => update,
    default: () => 'keyword_search',
  }),

  targetAction: Annotation<'retrieve_summary_context' | 'retrieve_context' | 'generate_answer'>({
    reducer: (current, update) => update,
    default: () => 'retrieve_context',
  }),

  needsKeywordGeneration: Annotation<boolean>({
    reducer: (current, update) => update,
    default: () => true,
  }),

  estimatedComplexity: Annotation<'simple' | 'moderate' | 'complex'>({
    reducer: (current, update) => update,
    default: () => 'moderate',
  }),

  // AI-determined optimal document count
  documentCount: Annotation<number>({
    reducer: (current, update) => update,
    default: () => 10,
  }),

  // Retry tracking
  retryCount: Annotation<number>({
    reducer: (current, update) => current + update,
    default: () => 0,
  }),

  maxRetries: Annotation<number>,

  // Final answer
  finalAnswer: Annotation<string>,

  // References for agent status display
  references: Annotation<RetrievedDocumentChunkDTO[]>({
    reducer: (current, update) => update,
    default: () => [],
  }),

  // Error handling
  error: Annotation<string | null>,

  // Configuration from chat
  config: Annotation<{
    spaceIds?: string[];
    documentIds?: string[];
    userId: string;
    chatId?: string;
  }>,

  // Selected context for better search
  selectedContext: Annotation<{
    documents?: { id: string; fileName: string }[];
    spaces?: { id: string; name: string; description?: string }[];
    filterScope: 'all' | 'documents' | 'spaces';
  }>({
    reducer: (current, update) => update,
    default: () => ({ filterScope: 'all' as const }),
  }),
});

// Derive the TypeScript type from the state annotation
export type AgentStateType = typeof AgentState.State;

// Simplified next action type for routing
export type NextAction =
  | 'generate_answer'
  | 'retrieve_summary'
  | 'refine_query'
  | 'fail_gracefully';
