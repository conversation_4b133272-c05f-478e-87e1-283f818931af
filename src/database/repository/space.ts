import { randomBytes } from 'crypto';
import { db } from '@/database/drizzle';
import {
  documents,
  documentSpaces,
  spaceInvitations,
  spaceMembers,
  spaceRoleEnum,
  spaces,
  users,
  type Document,
  type Space,
} from '@/database/schema';
import { and, desc, eq, isNull, sql } from 'drizzle-orm';

export type SpaceWithCount = Space & {
  documentCount: number;
};

type DocumentSpace = {
  id: number;
  documentId: string;
  spaceId: string;
  createdAt: Date;
  space: Space;
};

export type DocumentWithSpaces = Document & {
  documentSpaces: DocumentSpace[];
};

export type SpaceMember = {
  id: number;
  spaceId: string;
  userId: string;
  role: (typeof spaceRoleEnum.enumValues)[number];
  invitationId: number | null;
  createdAt: Date;
  updatedAt: Date;
};

export type SpaceInvitation = {
  id: number;
  spaceId: string;
  code: string;
  createdBy: string;
  useCount: number;
  createdAt: Date;
};

export class SpaceRepository {
  static async getSpacesByUserId(userId: string): Promise<SpaceWithCount[]> {
    const result = await db
      .select({
        id: spaces.id,
        name: spaces.name,
        description: spaces.description,
        ownerId: spaces.ownerId,
        isShared: spaces.isShared,
        createdAt: spaces.createdAt,
        updatedAt: spaces.updatedAt,
        deletedAt: spaces.deletedAt,
        documentCount: sql<number>`
          CAST(COUNT(DISTINCT 
            CASE 
              WHEN ${documentSpaces.documentId} IS NOT NULL 
              AND EXISTS (
                SELECT 1 FROM ${documents} 
                WHERE ${documents.id} = ${documentSpaces.documentId} 
                AND ${documents.deletedAt} IS NULL
              )
              THEN ${documentSpaces.documentId} 
              ELSE NULL 
            END
          ) AS INTEGER)
        `,
      })
      .from(spaces)
      .leftJoin(documentSpaces, eq(spaces.id, documentSpaces.spaceId))
      .where(and(eq(spaces.ownerId, userId), isNull(spaces.deletedAt)))
      .groupBy(spaces.id)
      .orderBy(spaces.createdAt);

    return result as SpaceWithCount[];
  }

  static async getDocumentsByUserId(userId: string): Promise<DocumentWithSpaces[]> {
    const docs = await db.query.documents.findMany({
      where: and(eq(documents.uploadedBy, userId), isNull(documents.deletedAt)),
      with: {
        documentSpaces: {
          with: {
            space: true,
          },
        },
      },
    });

    // Filter out document spaces where space is soft deleted
    return docs.map((doc) => ({
      ...doc,
      documentSpaces: doc.documentSpaces.filter((ds) => ds.space && ds.space.deletedAt === null),
    })) as DocumentWithSpaces[];
  }

  static async createSpace(
    name: string,
    description: string | null,
    ownerId: string
  ): Promise<Space> {
    const [space] = await db
      .insert(spaces)
      .values({
        name,
        description,
        ownerId,
      })
      .returning();
    return space;
  }

  static async updateSpace(id: string, name: string, description: string | null): Promise<Space> {
    const [space] = await db
      .update(spaces)
      .set({ name, description })
      .where(eq(spaces.id, id))
      .returning();
    return space;
  }

  static async deleteSpace(id: string): Promise<void> {
    await db.update(spaces).set({ deletedAt: new Date() }).where(eq(spaces.id, id));
  }

  static async addDocumentsToSpace(documentIds: string[], spaceIds: string[]): Promise<void> {
    const values = documentIds.flatMap((documentId) =>
      spaceIds.map((spaceId) => ({
        documentId,
        spaceId,
      }))
    );

    await db.insert(documentSpaces).values(values);
  }

  static async removeDocumentFromSpace(documentId: string, spaceId: string): Promise<void> {
    await db
      .delete(documentSpaces)
      .where(and(eq(documentSpaces.documentId, documentId), eq(documentSpaces.spaceId, spaceId)));
  }

  static async getSpaceDocuments(spaceId: string): Promise<Document[]> {
    const result = await db
      .select({
        id: documents.id,
        fileName: documents.fileName,
        fileType: documents.fileType,
        s3Key: documents.s3Key,
        uploadedBy: documents.uploadedBy,
        createdAt: documents.createdAt,
        updatedAt: documents.updatedAt,
        deletedAt: documents.deletedAt,
        summary: documents.summary,
        summaryEmbedding: documents.summaryEmbedding,
      })
      .from(documentSpaces)
      .innerJoin(documents, eq(documentSpaces.documentId, documents.id))
      .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
      .where(
        and(
          eq(documentSpaces.spaceId, spaceId),
          isNull(documents.deletedAt),
          isNull(spaces.deletedAt)
        )
      );

    return result;
  }

  // Toggle sharing status for a space
  static async toggleSharing(id: string, isShared: boolean): Promise<Space> {
    const [space] = await db.update(spaces).set({ isShared }).where(eq(spaces.id, id)).returning();
    return space;
  }

  // Add a member to a space with a specific role
  static async addMember(
    spaceId: string,
    userId: string,
    role: (typeof spaceRoleEnum.enumValues)[number] = 'viewer'
  ): Promise<SpaceMember> {
    // Check if the member already exists
    const existingMember = await db
      .select()
      .from(spaceMembers)
      .where(and(eq(spaceMembers.spaceId, spaceId), eq(spaceMembers.userId, userId)))
      .limit(1);

    if (existingMember.length > 0) {
      // Update role if member exists
      const [updatedMember] = await db
        .update(spaceMembers)
        .set({ role })
        .where(and(eq(spaceMembers.spaceId, spaceId), eq(spaceMembers.userId, userId)))
        .returning();
      return updatedMember as SpaceMember;
    }

    // Add new member
    const [member] = await db
      .insert(spaceMembers)
      .values({
        spaceId,
        userId,
        role,
      })
      .returning();
    return member as SpaceMember;
  }

  // Remove a member from a space
  static async removeMember(spaceId: string, userId: string): Promise<void> {
    await db
      .delete(spaceMembers)
      .where(and(eq(spaceMembers.spaceId, spaceId), eq(spaceMembers.userId, userId)));
  }

  // Get all members of a space
  static async getMembers(
    spaceId: string
  ): Promise<(SpaceMember & { userName: string; userEmail: string })[]> {
    const members = await db
      .select({
        id: spaceMembers.id,
        spaceId: spaceMembers.spaceId,
        userId: spaceMembers.userId,
        role: spaceMembers.role,
        invitationId: spaceMembers.invitationId,
        createdAt: spaceMembers.createdAt,
        updatedAt: spaceMembers.updatedAt,
        userName: users.name,
        userEmail: users.email,
      })
      .from(spaceMembers)
      .innerJoin(users, eq(spaceMembers.userId, users.id))
      .where(eq(spaceMembers.spaceId, spaceId))
      .orderBy(spaceMembers.createdAt);
    return members as (SpaceMember & { userName: string; userEmail: string })[];
  }

  // Create an invitation code for a space
  static async createInvitation(spaceId: string, createdBy: string): Promise<SpaceInvitation> {
    const code = randomBytes(16).toString('hex');

    const [invitation] = await db
      .insert(spaceInvitations)
      .values({
        spaceId,
        code,
        createdBy,
        useCount: 0,
      })
      .returning();
    return invitation as SpaceInvitation;
  }

  // Get all invitations for a space
  static async getInvitations(spaceId: string): Promise<SpaceInvitation[]> {
    const invitations = await db
      .select()
      .from(spaceInvitations)
      .where(and(eq(spaceInvitations.spaceId, spaceId), isNull(spaceInvitations.deletedAt)))
      .orderBy(desc(spaceInvitations.createdAt));
    return invitations as SpaceInvitation[];
  }

  // Delete an invitation
  static async deleteInvitation(id: number): Promise<void> {
    // Use soft delete instead of hard delete
    await db
      .update(spaceInvitations)
      .set({ deletedAt: new Date() })
      .where(eq(spaceInvitations.id, id));
  }

  // Validate an invitation code and join a space
  static async joinSpaceWithCode(
    code: string,
    userId: string
  ): Promise<{ success: boolean; message: string; spaceId?: string }> {
    // Find the invitation
    const [invitation] = await db
      .select({
        id: spaceInvitations.id,
        spaceId: spaceInvitations.spaceId,
        useCount: spaceInvitations.useCount,
      })
      .from(spaceInvitations)
      .where(eq(spaceInvitations.code, code))
      .limit(1);

    if (!invitation) {
      return { success: false, message: 'Invalid invitation code' };
    }

    // Check if space exists and is shared
    const [space] = await db
      .select()
      .from(spaces)
      .where(and(eq(spaces.id, invitation.spaceId), isNull(spaces.deletedAt)))
      .limit(1);

    if (!space) {
      return { success: false, message: 'Space not found' };
    }

    if (!space.isShared) {
      return { success: false, message: 'Space is not currently shared' };
    }

    // Check if user is already a member
    const existingMember = await db
      .select()
      .from(spaceMembers)
      .where(and(eq(spaceMembers.spaceId, invitation.spaceId), eq(spaceMembers.userId, userId)))
      .limit(1);

    if (existingMember.length > 0) {
      return {
        success: false,
        message: 'You are already a member of this space',
        spaceId: invitation.spaceId,
      };
    }

    // Add user as a member with viewer role and track the invitation used
    await db.insert(spaceMembers).values({
      spaceId: invitation.spaceId,
      userId,
      role: 'viewer',
      invitationId: invitation.id,
    });

    // Increment use count
    await db
      .update(spaceInvitations)
      .set({ useCount: invitation.useCount + 1 })
      .where(eq(spaceInvitations.id, invitation.id));

    return { success: true, message: 'Successfully joined space', spaceId: invitation.spaceId };
  }

  // Get spaces that a user is a member of (but not the owner)
  static async getSharedSpacesForUser(userId: string): Promise<SpaceWithCount[]> {
    const result = await db
      .select({
        id: spaces.id,
        name: spaces.name,
        description: spaces.description,
        ownerId: spaces.ownerId,
        isShared: spaces.isShared,
        createdAt: spaces.createdAt,
        updatedAt: spaces.updatedAt,
        deletedAt: spaces.deletedAt,
        documentCount: sql<number>`
          CAST(COUNT(DISTINCT 
            CASE 
              WHEN ${documentSpaces.documentId} IS NOT NULL 
              AND EXISTS (
                SELECT 1 FROM ${documents} 
                WHERE ${documents.id} = ${documentSpaces.documentId} 
                AND ${documents.deletedAt} IS NULL
              )
              THEN ${documentSpaces.documentId} 
              ELSE NULL 
            END
          ) AS INTEGER)
        `,
      })
      .from(spaces)
      .innerJoin(spaceMembers, eq(spaces.id, spaceMembers.spaceId))
      .leftJoin(documentSpaces, eq(spaces.id, documentSpaces.spaceId))
      .where(and(eq(spaceMembers.userId, userId), isNull(spaces.deletedAt)))
      .groupBy(spaces.id)
      .orderBy(spaces.createdAt);

    return result as SpaceWithCount[];
  }

  // Get all spaces available to a user (owned + member)
  static async getAllSpacesForUser(userId: string): Promise<SpaceWithCount[]> {
    const ownedSpaces = await this.getSpacesByUserId(userId);
    const sharedSpaces = await this.getSharedSpacesForUser(userId);

    // Combine and deduplicate spaces
    const allSpaces = [...ownedSpaces];

    for (const shared of sharedSpaces) {
      if (!allSpaces.some((space) => space.id === shared.id)) {
        allSpaces.push(shared);
      }
    }

    return allSpaces.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  }

  // Get all documents accessible to a user (owned + from shared spaces)
  static async getAllDocumentsForUser(userId: string): Promise<DocumentWithSpaces[]> {
    // Get user's own documents
    const ownDocuments = await this.getDocumentsByUserId(userId);

    // Get spaces the user is a member of
    const sharedSpaces = await this.getSharedSpacesForUser(userId);

    if (sharedSpaces.length === 0) {
      return ownDocuments;
    }

    // Get documents from shared spaces
    const sharedSpaceIds = sharedSpaces.map((space) => space.id);

    const sharedDocs = await db.query.documents.findMany({
      where: isNull(documents.deletedAt),
      with: {
        documentSpaces: {
          where: (ds, { inArray }) => {
            return inArray(ds.spaceId, sharedSpaceIds);
          },
          with: {
            space: true,
          },
        },
      },
    });

    // Filter docs to only include those that have document spaces in shared spaces
    const filteredSharedDocs = sharedDocs
      .filter((doc) => doc.documentSpaces.length > 0)
      .map((doc) => ({
        ...doc,
        documentSpaces: doc.documentSpaces.filter((ds) => ds.space && ds.space.deletedAt === null),
      })) as DocumentWithSpaces[];

    // Combine and deduplicate documents
    const allDocs = [...ownDocuments];

    for (const doc of filteredSharedDocs) {
      if (!allDocs.some((d) => d.id === doc.id)) {
        allDocs.push(doc);
      } else {
        // If the document is already in the array, merge the document spaces
        const existingDoc = allDocs.find((d) => d.id === doc.id);
        if (existingDoc) {
          // Merge documentSpaces without duplicates
          for (const ds of doc.documentSpaces) {
            if (!existingDoc.documentSpaces.some((existing) => existing.id === ds.id)) {
              existingDoc.documentSpaces.push(ds);
            }
          }
        }
      }
    }

    return allDocs;
  }
}
