import { MaterialRepository } from '@/database/repository/material';
import { SpaceRepository } from '@/database/repository/space';
import { documents } from '@/database/schema';
import { getUser } from '@/lib/auth';
import { processMaterialFromR2 } from '@/lib/materials/processor';
import { getDocumentType } from '@/lib/materials/utils';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const files = body.files as { key: string; name: string; type: string; size: number }[];
    const spaceIds = body.spaceIds as string[];

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files uploaded' }, { status: 400 });
    }

    // Process all files in parallel
    const results = await Promise.all(
      files.map(async (file) => {
        const fileKey = file.key;
        let material: typeof documents.$inferSelect | null = null;

        try {
          // Process material from R2 (download, chunk and generate embeddings)
          const { chunks, summary } = await processMaterialFromR2(
            fileKey,
            file.name,
            file.type,
            true
          ); // Enable debug

          // Store material in database with summary data
          material = await MaterialRepository.storeMaterial({
            fileName: file.name,
            fileType: getDocumentType(file.type),
            s3Key: fileKey,
            uploadedBy: user.id,
            summary: summary?.summary || null,
            summaryEmbedding: summary?.summaryEmbedding || null,
          });

          // Store chunks
          await MaterialRepository.storeChunks({
            documentId: material.id,
            chunks: chunks.map((chunk) => ({
              ...chunk,
              content: chunk.content.replace(/\x00/g, ''), // Remove null bytes
            })),
          });

          // If spaceIds are provided, create document-space connections
          if (spaceIds.length > 0) {
            await SpaceRepository.addDocumentsToSpace([material.id], spaceIds);
          }

          return {
            success: true,
            fileName: file.name,
            materialId: material.id,
          };
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);

          // If material was created, clean up
          if (material?.id) {
            try {
              await MaterialRepository.deleteMaterial(material.id, fileKey);
            } catch (cleanupError) {
              console.error(`Error during cleanup for file ${file.name}:`, cleanupError);
            }
          }

          return {
            success: false,
            fileName: file.name,
            error: error instanceof Error ? error.message : 'Error processing file',
          };
        }
      })
    );

    const successfulUploads = results.filter((result) => result.success);
    const failedUploads = results.filter((result) => !result.success);

    if (successfulUploads.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'All uploads failed',
          failedUploads,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      uploadedFiles: successfulUploads.map((result) => result.fileName),
      failedUploads: failedUploads.length > 0 ? failedUploads : undefined,
    });
  } catch (error) {
    console.error('Error processing upload:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
