import { db } from '@/database/drizzle';
import { interactions, topics } from '@/database/schema';
import type { AgentStep } from '@/hooks/use-chat-enhanced';
import { getUser } from '@/lib/auth';
import { runRAGAgent, type CustomEvent } from '@/lib/langgraph/graph';
import type { AgentStateType } from '@/lib/langgraph/types';
import { ChatConfig } from '@/types/chat';
import { eq } from 'drizzle-orm';
import { NextRequest } from 'next/server';

/**
 * Aggregates and deduplicates retrieved documents from agent steps for debugging purposes
 */
function aggregateRetrievedDocuments(agentSteps: AgentStep[]): unknown[] {
  const documentMap = new Map<string, Record<string, unknown>>();

  for (const step of agentSteps) {
    // Process retrieved chunks
    if (step.retrievedChunks) {
      for (const chunk of step.retrievedChunks) {
        const key = `chunk_${chunk.id}_${chunk.chunkId}`;
        if (!documentMap.has(key)) {
          documentMap.set(key, {
            type: 'chunk',
            id: chunk.id,
            chunkId: chunk.chunkId,
            fileName: chunk.fileName,
            key: chunk.key,
            fileType: chunk.fileType,
            similarity: chunk.similarity,
            metadata: chunk.metadata,
          });
        }
      }
    }

    // Process retrieved summaries
    if (step.retrievedSummaries) {
      for (const summary of step.retrievedSummaries) {
        const key = `summary_${summary.id}`;
        if (!documentMap.has(key)) {
          documentMap.set(key, {
            type: 'summary',
            id: summary.id,
            fileName: summary.fileName,
            key: summary.key,
            fileType: summary.fileType,
            similarity: summary.similarity,
          });
        }
      }
    }
  }

  return Array.from(documentMap.values());
}

/**
 * Creates an optimized agent step with only relevant data for each step type
 * This prevents storing redundant data across all steps and significantly reduces database storage
 */
function createOptimizedAgentStep(
  nodeName: string,
  displayMessage: string,
  state: AgentStateType
): AgentStep {
  // Base step data that all steps need
  const baseStep: AgentStep = {
    step: nodeName,
    message: displayMessage,
    retryCount: state.retryCount || 0,
    hasError: !!state.error,
    error: state.error || undefined,
  };

  // Add step-specific data based on the node type
  switch (nodeName) {
    case 'classify_query':
      return {
        ...baseStep,
        reasoning: state.reasoning || undefined,
      };

    case 'check_history_relevance':
      return {
        ...baseStep,
        reasoning: state.historyReasoning || undefined,
        needsHistory: state.needsHistory || undefined,
      };

    case 'create_research_plan':
      return {
        ...baseStep,
        reasoning: state.reasoning || undefined,
        retrievalStrategy: state.retrievalStrategy || undefined,
      };

    case 'resolve_material_context':
      return {
        ...baseStep,
        // Frontend groups this with retrieval steps and only shows final data
        // Only store essential data for this intermediate step
        reasoning: state.reasoning || undefined,
        retrievalStrategy: state.retrievalStrategy || undefined,
      };

    case 'retrieve_context':
    case 'retrieve_summary_context':
      return {
        ...baseStep,
        reasoning: state.reasoning || undefined,
        extractedKeywords: state.refinedKeywords || undefined,
        retrievedChunks: state.retrievedChunks?.map((chunk) => ({
          id: chunk.id,
          chunkId: chunk.chunkId || 0,
          fileName: chunk.fileName,
          content: chunk.content.substring(0, 200) + '...',
          key: chunk.key,
          similarity: chunk.similarity,
          fileType: chunk.fileType.toString(),
          metadata: (chunk.metadata || {}) as import('@/types/chat').DocumentMetadata,
        })),
        retrievedSummaries: state.retrievedSummaries?.map((summary) => ({
          id: summary.id,
          fileName: summary.fileName,
          summary: summary.summary.substring(0, 200) + '...',
          key: summary.key,
          similarity: summary.similarity,
          fileType: summary.fileType,
        })),
        retrievalStrategy: state.retrievalStrategy || undefined,
        targetMaterials: state.targetMaterials || undefined,
        materialSelectionReasoning: state.materialSelectionReasoning || undefined,
      };

    case 'evaluate_results':
      return {
        ...baseStep,
        reasoning: state.reasoning || undefined,
        // Frontend only shows reasoning for evaluate_results - no chunks/summaries needed
      };

    case 'refine_query':
      return {
        ...baseStep,
        reasoning: state.reasoning || undefined,
        extractedKeywords: state.refinedKeywords || undefined,
      };

    case 'generate_answer':
      return {
        ...baseStep,
        // Frontend doesn't show generate_answer step at all - minimal data needed
        reasoning: state.reasoning || undefined,
      };

    default:
      // For any unknown steps, just return base data
      return baseStep;
  }
}

/**
 * OPTIMIZATION BENEFITS (Based on Frontend Usage Analysis):
 *
 * Before: Each step stored ALL state data (~15-20 fields per step)
 * - classify_query: 15+ fields (mostly empty/duplicated)
 * - create_research_plan: 15+ fields (mostly empty/duplicated)
 * - resolve_material_context: 15+ fields (mostly empty/duplicated)
 * - retrieve_context: 15+ fields (all data duplicated)
 * - evaluate_results: 15+ fields (all data duplicated)
 * - generate_answer: 15+ fields (all data duplicated)
 *
 * After: Each step stores only data ACTUALLY USED by frontend (2-8 fields per step)
 * - classify_query: 5 fields (base + reasoning) ✓ Frontend shows reasoning
 * - create_research_plan: 6 fields (base + reasoning + strategy) ✓ Grouped with retrieval
 * - resolve_material_context: 6 fields (base + reasoning + strategy) ✓ Grouped with retrieval
 * - retrieve_context: 8 fields (base + keywords + chunks + summaries + strategy) ✓ Frontend shows all
 * - evaluate_results: 5 fields (base + reasoning) ✓ Frontend only shows reasoning
 * - generate_answer: 5 fields (base + reasoning) ✓ Frontend doesn't show this step at all
 *
 * Key Optimizations:
 * - Removed chunks/summaries from evaluate_results (not displayed)
 * - Removed chunks/summaries from generate_answer (step not displayed)
 * - Removed targetMaterials from resolve_material_context (grouped with retrieval)
 *
 * Expected storage reduction: ~70-85% less data stored in database
 * Performance improvement: Faster queries, less memory usage, cleaner UI
 */

export async function POST(req: NextRequest, { params }: { params: { chatId: string } }) {
  const session = await getUser();
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const { content, isInitialMessage, config } = await req.json();

    // First, get the existing topic to retrieve its stored config
    const existingTopic = await db.query.topics.findFirst({
      where: eq(topics.id, params.chatId),
      columns: {
        id: true,
        config: true,
        userId: true,
      },
    });

    if (!existingTopic) {
      return new Response('Chat not found', { status: 404 });
    }

    // Ensure user owns the topic
    if (existingTopic.userId !== session.user?.id) {
      return new Response('Unauthorized', { status: 403 });
    }

    // Merge the stored config with any provided config, giving priority to provided config
    const storedConfig = existingTopic.config as ChatConfig;
    const providedConfig = config as ChatConfig;

    // Default values
    const defaultConfig: ChatConfig = {
      searchMode: 'semantic' as const,
      spaceIds: [],
      documentIds: [],
    };

    const chatConfig: ChatConfig = {
      ...defaultConfig,
      ...storedConfig,
      // Allow all fields from provided config to override stored config
      ...(providedConfig && providedConfig),
    };

    // Update topic's config only if it has changed (to save bandwidth)
    if (config) {
      const configChanged =
        JSON.stringify(storedConfig?.spaceIds?.sort()) !==
          JSON.stringify(chatConfig.spaceIds?.sort()) ||
        JSON.stringify(storedConfig?.documentIds?.sort()) !==
          JSON.stringify(chatConfig.documentIds?.sort()) ||
        storedConfig?.searchMode !== chatConfig.searchMode;

      if (configChanged) {
        console.log('💾 Updating chat config in database - config has changed');
        await db.update(topics).set({ config: chatConfig }).where(eq(topics.id, params.chatId));
      } else {
        console.log('🔄 Skipping config update - no changes detected');
      }
    }

    // Only save user message if it's not an initial message
    if (!isInitialMessage && content) {
      await db.insert(interactions).values({
        topicId: params.chatId,
        userId: session.user?.id ?? '',
        content,
        senderType: 'user',
      });
    }

    // Create a readable stream for real-time updates
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        let finalAnswer = '';
        const progressUpdates: AgentStep[] = [];
        let currentStep = '';

        try {
          // Custom event handler for real-time updates
          const handleCustomEvent = (event: CustomEvent) => {
            try {
              switch (event.type) {
                case 'step_start':
                  currentStep = event.step;

                  // Create agent step for this step start
                  const agentStep: AgentStep = {
                    step: event.step,
                    message: event.message,
                    retryCount: 0,
                    hasError: false,
                  };
                  progressUpdates.push(agentStep);

                  // Send step start event to client
                  const stepStartMessage = `data: ${JSON.stringify({
                    type: 'step_start',
                    step: event.step,
                    message: event.message,
                  })}\n\n`;
                  controller.enqueue(encoder.encode(stepStartMessage));
                  break;

                case 'content_chunk':
                  // Stream content using consistent data format
                  const contentMessage = `data: ${JSON.stringify({
                    type: 'content',
                    content: event.content,
                  })}\n\n`;
                  controller.enqueue(encoder.encode(contentMessage));
                  finalAnswer += event.content;
                  break;

                case 'step_complete':
                  // Mark step as complete if we want to track this
                  break;

                default:
                  console.log('Unhandled custom event type:', event.type);
              }
            } catch (eventError) {
              console.error('Custom event handling error:', eventError);
            }
          };

          // Progress callback for state updates
          const onProgress = (nodeName: string, state: AgentStateType) => {
            try {
              // Send detailed progress update with state information
              const displayMessage = getDisplayMessage(nodeName);

              // Create optimized agent step with only relevant data for each step type
              const agentStep: AgentStep = createOptimizedAgentStep(
                nodeName,
                displayMessage,
                state
              );

              // Update our progress tracking
              const existingStepIndex = progressUpdates.findIndex((step) => step.step === nodeName);
              if (existingStepIndex >= 0) {
                // Update existing step with more details
                progressUpdates[existingStepIndex] = {
                  ...progressUpdates[existingStepIndex],
                  ...agentStep,
                };
              } else {
                // Add new step if it doesn't exist
                progressUpdates.push(agentStep);
              }

              // Send progress update to client with detailed information
              const progressMessage = `data: ${JSON.stringify({
                type: 'progress',
                step: nodeName,
                message: displayMessage,
                retryCount: state.retryCount || 0,
                hasError: !!state.error,
                extractedKeywords: state.refinedKeywords,
                reasoning: state.reasoning || undefined,
                retrievedChunks: agentStep.retrievedChunks,
                retrievedSummaries: agentStep.retrievedSummaries,
                error: state.error || undefined,
              })}\n\n`;

              controller.enqueue(encoder.encode(progressMessage));
            } catch (progressError) {
              console.error('Progress update error:', progressError);
            }
          };

          // Helper function to get display message for a node
          const getDisplayMessage = (nodeName: string): string => {
            switch (nodeName) {
              case 'classify_query':
                return 'Understanding your question...';
              case 'analyze_query':
                return 'Analyzing your question...';
              case 'retrieve_documents':
                return 'Searching through documents...';
              case 'evaluate_results':
                return 'Evaluating search results...';
              case 'refine_query':
                return 'Refining search strategy...';
              case 'retrieve_summary':
                return 'Analyzing document summaries...';
              case 'generate_answer':
                return 'Generating your answer...';
              case 'generate_direct_response':
                return 'Generating response...';
              case 'handle_failure':
                return 'Processing failed';
              default:
                return `${nodeName.replace(/_/g, ' ')}...`;
            }
          };

          // Run the RAG agent with custom event handling
          const result = await runRAGAgent(
            content,
            {
              spaceIds: chatConfig.spaceIds,
              documentIds: chatConfig.documentIds,
              userId: session.user?.id ?? '',
              chatId: params.chatId,
            },
            onProgress,
            handleCustomEvent
          );

          finalAnswer = result.finalAnswer;

          // Aggregate and deduplicate retrieved documents from agent steps for debugging
          const aggregatedDocuments = aggregateRetrievedDocuments(progressUpdates);

          // Send completion message using consistent data format
          const completionMessage = `data: ${JSON.stringify({
            type: 'completion',
            progressSteps: progressUpdates,
            finalStep: currentStep,
          })}\n\n`;

          controller.enqueue(encoder.encode(completionMessage));

          // Save the AI response to database with agent steps
          await db.insert(interactions).values({
            topicId: params.chatId,
            userId: session.user?.id ?? '',
            content: finalAnswer,
            senderType: 'ai',
            retrievedDocuments: aggregatedDocuments, // Aggregated documents for debugging
            agentSteps: progressUpdates,
          });

          controller.close();
        } catch (error) {
          console.error('❌ LangGraph chat error:', error);

          // Send error message
          const errorMessage =
            'I encountered an error while processing your request. Please try again.';
          controller.enqueue(encoder.encode(errorMessage));

          const errorData = JSON.stringify({
            type: 'error',
            message: error instanceof Error ? error.message : 'Unknown error',
          });
          controller.enqueue(encoder.encode(`\nEOM${errorData}`));

          // Save error response with any progress steps collected
          await db.insert(interactions).values({
            topicId: params.chatId,
            userId: session.user?.id ?? '',
            content: errorMessage,
            senderType: 'ai',
            agentSteps: progressUpdates.length > 0 ? progressUpdates : undefined,
          });

          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    });
  } catch (error) {
    console.error('❌ Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
