import SearchPage from '@/components/search/search-page';
import { SITE_PATHS } from '@/configs/site';
import { SpaceRepository } from '@/database/repository/space';
import { getUser } from '@/lib/auth';
import { env } from '@/lib/env.mjs';
import { searchMaterialsWithCount } from '@/lib/materials/search';
import { FinderMode } from '@/types/search';
import { redirect } from 'next/navigation';

type SearchParams = {
  q?: string;
  mode?: 'finder' | 'chat';
  finderMode?: FinderMode;
  spaceIds?: string;
  documentIds?: string;
  limit?: string;
  page?: string;
  pageSize?: string;
};

export default async function Search({ searchParams }: { searchParams: SearchParams }) {
  const { user } = await getUser();
  if (!user) {
    redirect(SITE_PATHS.AUTH.SIGN_IN);
  }

  const query = searchParams.q || '';
  const mode = searchParams.mode || 'finder';
  // Parse filter parameters - distinguish between undefined (no filter) and empty string (filter applied but empty)
  const requestedSpaceIds =
    searchParams.spaceIds !== undefined
      ? searchParams.spaceIds.split(',').filter(Boolean)
      : undefined;
  const requestedDocumentIds =
    searchParams.documentIds !== undefined
      ? searchParams.documentIds.split(',').filter(Boolean)
      : undefined;
  const page = searchParams.page ? parseInt(searchParams.page, 10) : 1;
  const pageSize = searchParams.pageSize ? parseInt(searchParams.pageSize, 10) : 20;
  const offset = (page - 1) * pageSize;

  // Get all spaces the user has access to
  const accessibleSpaces = await SpaceRepository.getAllSpacesForUser(user.id);
  const accessibleSpaceIds = accessibleSpaces
    .filter(
      (space) =>
        // Only include spaces that:
        // 1. Are owned by the user, or
        // 2. Have sharing enabled
        space.ownerId === user.id || space.isShared
    )
    .map((space) => space.id);

  // Handle space and document filtering logic
  let spaceIds: string[] | undefined;
  let documentIds: string[] | undefined;

  if (requestedDocumentIds !== undefined) {
    // Document filter is active - use the requested documents (even if empty array)
    documentIds = requestedDocumentIds;
    spaceIds = undefined; // Don't apply space filtering when document filter is active
  } else if (requestedSpaceIds !== undefined) {
    // Space filter is active - filter to only include accessible spaces
    spaceIds = requestedSpaceIds.filter((id) => accessibleSpaceIds.includes(id));
    documentIds = undefined;
  } else {
    // No specific filters were applied - search all accessible spaces
    spaceIds = accessibleSpaceIds;
    documentIds = undefined;
  }

  // Debug logging for search parameters
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('🔍 Search Debug Info:');
  //   console.log('  Query:', query);
  //   console.log('  Mode:', mode);
  //   console.log('  Finder Mode:', searchParams.finderMode || 'fulltext');
  //   console.log('  Requested Space IDs:', requestedSpaceIds);
  //   console.log('  Requested Document IDs:', requestedDocumentIds);
  //   console.log('  Final Space IDs:', spaceIds);
  //   console.log('  Final Document IDs:', documentIds);
  //   console.log('  Page:', page, 'Page Size:', pageSize, 'Offset:', offset);
  //   console.log('  User ID:', user.id);
  // }

  const searchResults =
    mode === 'finder' && query && query.length > 0 && query.trim()
      ? await searchMaterialsWithCount({
          query,
          mode: searchParams.finderMode || 'fulltext',
          spaceIds,
          documentIds,
          limit: pageSize,
          offset,
          userId: user.id, // Pass the user ID for access control
          debug: process.env.NODE_ENV === 'development', // Enable debug logging in development
        })
      : null;

  return (
    <SearchPage
      initialQuery={query}
      mode={mode}
      finderMode={searchParams.finderMode || 'fulltext'}
      searchResults={searchResults}
      materialAccessUrl={env.NEXT_PUBLIC_MATERIAL_ACCESS_URL}
    />
  );
}
