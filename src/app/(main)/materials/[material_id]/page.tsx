import { DocumentViewer } from '@/components/documents/document-viewer';
import { MaterialSummary } from '@/components/materials/material-summary';
import { Card } from '@/components/ui/card';
import { MaterialRepository } from '@/database/repository/material';
import { env } from '@/lib/env.mjs';
import { DateTime } from 'luxon';

type MaterialPageProps = {
  params: {
    material_id: string;
  };
};

export default async function MaterialPage({ params }: MaterialPageProps) {
  if (!params.material_id) {
    return <div>Material ID required</div>;
  }

  const material = await MaterialRepository.getMaterialWithChunksById(params.material_id);
  if (!material) {
    return <div>{`Material ${params.material_id} not found`}</div>;
  }
  return (
    <div className="flex h-full max-h-[calc(100vh-4.1rem)] overflow-hidden">
      <div className="w-1/2 bg-gray-100">
        <DocumentViewer
          url={`${env.NEXT_PUBLIC_MATERIAL_ACCESS_URL}/${material.s3Key}`}
          fileType={material.fileType}
        />
      </div>

      <div className="flex w-1/2 flex-col">
        <div className="flex items-center justify-between border border-t-0 bg-white p-2">
          <div className="">{material.fileName}</div>
          <div className="text-sm text-gray-500">
            {DateTime.fromJSDate(new Date(material.createdAt)).toFormat('yyyy/MM/dd')}
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {/* Document Summary */}
          <MaterialSummary summary={material.summary} className="m-2 mb-2" />

          {/* Document Chunks */}
          {material.chunks.map((chunk) => (
            <Card key={chunk.id} className="rounded-none border border-t-0 p-4">
              <div className="space-y-2">
                <div className="whitespace-pre-wrap text-sm text-gray-700">{chunk.content}</div>
                {/* <div className="text-xs text-gray-500">Page: {chunk.pageNumber}</div> */}
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
