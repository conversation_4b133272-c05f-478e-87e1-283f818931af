'use client';

import { ShareSpaceDialog } from '@/components/space/share-space-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { SpaceWithCount } from '@/database/repository/space';
import { clientApi } from '@/lib/trpc/client-api';
import { formatDate } from '@/lib/utils';
import { useUserStore } from '@/stores/user';
import { MoreHorizontal, Pencil, Share2, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useState } from 'react';
import { EditSpaceDialog } from './edit-space-dialog';

type SpaceItemProps = {
  space: SpaceWithCount;
};

function SpaceItem({ space }: SpaceItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const utils = clientApi.useUtils();
  const currentUser = useUserStore((state) => state.user);

  const deleteMutation = clientApi.space.delete.useMutation({
    onSuccess: () => {
      toast.success('Space deleted successfully');
      utils.space.list.invalidate();
    },
    onError: (error) => {
      toast.error('Failed to delete space');
      console.error(error);
    },
  });

  async function onDelete() {
    if (!confirm('Are you sure you want to delete this space?')) return;
    await deleteMutation.mutateAsync(space.id);
  }

  // Determine the sharing status and role
  const isOwner = space.ownerId === currentUser?.id;
  const sharingStatus = () => {
    if (isOwner) {
      return space.isShared ? 'Shared' : 'Private';
    } else {
      // This is a simplified approach - in a full implementation you might
      // want to fetch the actual role from spaceMembers
      return 'Member';
    }
  };

  const getBadgeVariant = () => {
    if (isOwner) {
      return space.isShared ? 'default' : 'secondary';
    } else {
      return 'outline';
    }
  };

  return (
    <>
      <TableRow>
        <TableCell className="font-medium">{space.name}</TableCell>
        <TableCell>{space.description}</TableCell>
        <TableCell>
          <Badge variant="outline">{space.documentCount} documents</Badge>
        </TableCell>
        <TableCell>
          <Badge variant={getBadgeVariant()}>{sharingStatus()}</Badge>
        </TableCell>
        <TableCell>{formatDate(space.createdAt)}</TableCell>
        <TableCell>
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setIsEditing(true)}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                {isOwner && (
                  <DropdownMenuItem
                    onClick={() => setIsSharing(true)}
                    className="flex items-center"
                  >
                    <Share2 className="mr-2 h-4 w-4" />
                    Share
                  </DropdownMenuItem>
                )}
                {isOwner && (
                  <DropdownMenuItem onClick={onDelete} className="text-red-600 focus:text-red-600">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableCell>
      </TableRow>

      <EditSpaceDialog space={space} open={isEditing} onOpenChange={setIsEditing} />
      {isOwner && (
        <ShareSpaceDialog
          isOpen={isSharing}
          onClose={() => setIsSharing(false)}
          spaceId={space.id}
          spaceName={space.name}
          isShared={space.isShared ?? false}
        />
      )}
    </>
  );
}

type SpaceListProps = {
  spaces: SpaceWithCount[];
};

export function SpaceList({ spaces }: SpaceListProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredSpaces = spaces.filter((space) =>
    space.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (spaces.length === 0) {
    return <div className="text-center text-muted-foreground">No spaces created yet</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Input
          placeholder="Search spaces..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-xs"
        />
      </div>

      {filteredSpaces.length === 0 ? (
        <div className="text-center text-muted-foreground">No spaces found</div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Documents</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="w-[70px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSpaces.map((space) => (
              <SpaceItem key={space.id} space={space} />
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
