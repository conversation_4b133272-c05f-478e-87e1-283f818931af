'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { MultiSelect } from '@/components/ui/multi-select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { DocumentWithSpaces } from '@/database/repository/space';
import { clientApi } from '@/lib/trpc/client-api';
import { formatDate } from '@/lib/utils';
import { useUserStore } from '@/stores/user';
import { ArrowUpDown, ExternalLink, MoreHorizontal } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useMemo, useState } from 'react';
import { AddToSpaceDialog } from './add-to-space-dialog';
import { EditFilenameDialog } from './edit-filename-dialog';

type MaterialItemProps = {
  material: DocumentWithSpaces;
};

function MaterialItem({ material }: MaterialItemProps) {
  const utils = clientApi.useUtils();
  const currentUser = useUserStore((state) => state.user);
  const [isAddToSpaceOpen, setIsAddToSpaceOpen] = useState(false);
  const [isEditFilenameOpen, setIsEditFilenameOpen] = useState(false);

  const deleteMutation = clientApi.material.delete.useMutation({
    onSuccess: () => {
      toast.success('Material deleted successfully');
      utils.material.list.invalidate();
    },
    onError: (error) => {
      toast.error('Failed to delete material');
      console.error(error);
    },
  });

  async function onDelete() {
    if (!confirm('Are you sure you want to delete this material?')) return;
    await deleteMutation.mutateAsync(material.id);
  }

  // Determine if any of the spaces this material belongs to are shared
  const isInSharedSpace = material.documentSpaces.some((ds) => ds.space.isShared);

  // Determine if user is owner of any spaces this material belongs to
  const isOwnerOfAnySpace = material.documentSpaces.some(
    (ds) => ds.space.ownerId === currentUser?.id
  );

  // Determine sharing status text and badge variant
  const getSharingStatus = () => {
    if (!material.documentSpaces.length) return 'No Space';
    if (!isInSharedSpace) return 'Private';
    if (isOwnerOfAnySpace) return 'Shared';
    return 'Member Access';
  };

  const getBadgeVariant = () => {
    if (!material.documentSpaces.length) return 'outline';
    if (!isInSharedSpace) return 'secondary';
    if (isOwnerOfAnySpace) return 'default';
    return 'outline';
  };

  return (
    <>
      <TableRow>
        <TableCell>
          <Link
            href={`/materials/${material.id}`}
            className="flex w-fit items-center gap-2 font-medium text-foreground hover:underline"
          >
            {material.fileName}
            <ExternalLink className="h-3 w-3 text-gray-400" />
          </Link>
        </TableCell>
        <TableCell>
          {material.documentSpaces.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {material.documentSpaces.map((ds) => (
                <Badge key={ds.id} variant="outline">
                  {ds.space.name}
                </Badge>
              ))}
            </div>
          ) : (
            <span className="text-muted-foreground">No spaces</span>
          )}
        </TableCell>
        <TableCell>
          <Badge variant={getBadgeVariant()}>{getSharingStatus()}</Badge>
        </TableCell>
        <TableCell>{formatDate(material.createdAt)}</TableCell>
        <TableCell>
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/materials/${material.id}`}>View</Link>
                </DropdownMenuItem>
                {material.uploadedBy === currentUser?.id && (
                  <DropdownMenuItem onClick={() => setIsEditFilenameOpen(true)}>
                    Edit Filename
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => setIsAddToSpaceOpen(true)}>
                  Add to Space
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {material.uploadedBy === currentUser?.id && (
                  <DropdownMenuItem onClick={onDelete} className="text-red-600 focus:text-red-600">
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableCell>
      </TableRow>

      <AddToSpaceDialog
        open={isAddToSpaceOpen}
        onOpenChange={setIsAddToSpaceOpen}
        material={material}
      />

      <EditFilenameDialog
        open={isEditFilenameOpen}
        onOpenChange={setIsEditFilenameOpen}
        materialId={material.id}
        currentFileName={material.fileName}
      />
    </>
  );
}

type MaterialListProps = {
  materials: DocumentWithSpaces[];
};

export function MaterialList({ materials }: MaterialListProps) {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedSpaces, setSelectedSpaces] = useState<string[]>([]);

  const filteredAndSortedMaterials = useMemo(() => {
    return materials
      .filter((material) => material.fileName.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      });
  }, [materials, searchTerm, sortOrder]);

  // Get unique spaces from all materials using space ID
  const allSpaces = Array.from(
    new Map(
      materials
        .flatMap((material) => material.documentSpaces.map((ds) => ds.space))
        .map((space) => [space.id, space])
    ).values()
  ).sort((a, b) => a.name.localeCompare(b.name));

  // Filter materials based on selected spaces
  const filteredMaterials = filteredAndSortedMaterials.filter((material) =>
    selectedSpaces.length === 0
      ? true
      : material.documentSpaces.some((ds) => selectedSpaces.includes(ds.space.id))
  );

  if (materials.length === 0) {
    return <div className="text-center text-muted-foreground">No materials uploaded yet</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="flex flex-1 items-center gap-2">
          <Input
            type="text"
            placeholder="Search by filename"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-xs"
          />
          <Button
            variant="outline"
            size="icon"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <MultiSelect
            options={allSpaces.map((space) => ({
              label: space.name,
              value: space.id,
            }))}
            value={selectedSpaces}
            onValueChange={setSelectedSpaces}
            placeholder="All spaces"
            modalPopover={true}
          />
        </div>
      </div>

      {filteredMaterials.length === 0 ? (
        <div className="text-center text-muted-foreground">No materials found</div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Spaces</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Uploaded At</TableHead>
              <TableHead className="w-[70px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMaterials.map((material) => (
              <MaterialItem key={material.id} material={material} />
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
