'use client';

import { DocumentType } from '@/types/material';
import { DocsViewer } from './doc-viewer';
import { PDFViewer, PDFViewerOptions } from './pdf-viewer';

export type DocumentViewerOptions = PDFViewerOptions;

type DocumentViewerProps = {
  url?: string;
  fileType?: DocumentType;
  options?: DocumentViewerOptions;
};

export function DocumentViewer({ url, fileType, options }: DocumentViewerProps) {
  if (!url || !fileType) return null;

  if (fileType === DocumentType.PDF) {
    return <PDFViewer url={url} options={options} />;
  } else {
    return <DocsViewer url={url} />;
  }
}
