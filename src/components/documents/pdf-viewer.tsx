'use client';

import { Viewer } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { pageNavigationPlugin } from '@react-pdf-viewer/page-navigation';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { useEffect, useRef, useState } from 'react';

// Cache for storing PDF blobs
const pdfCache = new Map<string, Blob>();
const blobUrlCache = new Map<string, string>();

type PDFViewerProps = {
  url: string;
  options?: PDFViewerOptions;
};

export type PDFViewerOptions = {
  initialPage?: number;
  timestamp?: number;
};

export function PDFViewer({ url, options }: PDFViewerProps) {
  const pageNavigationPluginInstance = useRef(pageNavigationPlugin()).current;
  const defaultLayoutPluginInstance = useRef(defaultLayoutPlugin()).current;
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const currentPdfUrl = useRef<string | null>(null);
  const [isDocumentLoaded, setIsDocumentLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load and cache PDF data
  useEffect(() => {
    const loadPDF = async () => {
      console.log('PDFViewer: Loading PDF from URL:', url);
      // Reset states when URL changes
      setIsDocumentLoaded(false);
      setError(null);
      setIsLoading(true);

      // Check if we already have a blob URL for this PDF
      if (blobUrlCache.has(url)) {
        const cachedUrl = blobUrlCache.get(url)!;
        setPdfUrl(cachedUrl);
        currentPdfUrl.current = cachedUrl;
        setIsLoading(false);
        return;
      }

      try {
        let pdfBlob: Blob;

        // Check if we have the PDF blob in cache
        if (pdfCache.has(url)) {
          pdfBlob = pdfCache.get(url)!;
        } else {
          // Fetch and cache the PDF blob with proper headers
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              Accept: 'application/pdf',
            },
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
          }

          pdfBlob = await response.blob();

          // Verify it's actually a PDF
          if (!pdfBlob.type.includes('pdf') && pdfBlob.type !== 'application/octet-stream') {
            console.warn('Response is not a PDF, got:', pdfBlob.type);
          }

          pdfCache.set(url, pdfBlob);
        }

        // Create and cache the blob URL
        const blobUrl = URL.createObjectURL(pdfBlob);
        blobUrlCache.set(url, blobUrl);
        setPdfUrl(blobUrl);
        currentPdfUrl.current = blobUrl;
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading PDF:', error);
        console.error('URL:', url);
        setError(error instanceof Error ? error.message : 'Failed to load PDF');
        setIsLoading(false);
      }
    };

    loadPDF();

    // Cleanup function
    return () => {
      const urlToCleanup = currentPdfUrl.current;
      if (urlToCleanup) {
        const usageCount = Array.from(blobUrlCache.values()).filter(
          (v) => v === urlToCleanup
        ).length;
        if (usageCount <= 1) {
          URL.revokeObjectURL(urlToCleanup);
          blobUrlCache.delete(url);
        }
      }
    };
  }, [url]);

  // Handle page navigation after document is loaded or when timestamp changes
  useEffect(() => {
    if (isDocumentLoaded && options?.initialPage) {
      pageNavigationPluginInstance.jumpToPage(options.initialPage - 1);
    }
  }, [isDocumentLoaded, options?.initialPage, options?.timestamp, pageNavigationPluginInstance]);

  const handleDocumentLoad = () => {
    setIsDocumentLoaded(true);
  };

  if (error) {
    return (
      <div className="flex h-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-2 text-red-600">Failed to load PDF</div>
          <div className="text-sm text-gray-500">{error}</div>
          <div className="mt-2 text-xs text-gray-400">URL: {url}</div>
        </div>
      </div>
    );
  }

  if (isLoading || !pdfUrl) {
    return (
      <div className="flex h-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <div className="text-gray-600">Loading PDF...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <Viewer
        fileUrl={pdfUrl}
        defaultScale={1}
        enableSmoothScroll
        plugins={[defaultLayoutPluginInstance, pageNavigationPluginInstance]}
        onDocumentLoad={handleDocumentLoad}
      />
    </div>
  );
}
