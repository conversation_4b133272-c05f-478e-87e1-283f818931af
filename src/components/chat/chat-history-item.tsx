'use client';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenuAction, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { MoreHorizontal, Trash } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import * as React from 'react';
import { UserTopic } from '@/app/api/chats/route';

export type ChatHistoryItemProps = {
  chat: UserTopic;
};

export function ChatHistoryItem({ chat }: ChatHistoryItemProps) {
  const params = useParams();
  const router = useRouter();
  const isActive = params.chatId === chat.id;

  return (
    <SidebarMenuItem className="group/item relative flex w-full justify-between">
      <SidebarMenuButton
        isActive={isActive}
        // onClick={() => router.push(`/chat/${chat.id}`)}
        onClick={() => router.push(`/chat/${chat.id}/enhanced`)}
        className="flex w-full items-center justify-between py-3"
        tooltip={chat.name}
      >
        <span className="truncate font-medium">{chat.name}</span>

        <SidebarMenuAction asChild className="absolute right-2 top-1/2 -translate-y-1/2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="h-8 w-8 p-0 opacity-0 transition-opacity group-hover/item:opacity-100">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More</span>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem className="text-destructive" disabled>
                <Trash className="mr-2 h-4 w-4" />
                Delete Topic (Will be available in the future)
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuAction>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
