import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { clientApi } from '@/lib/trpc/client-api';
import { cn } from '@/lib/utils';
import { useSearchStore } from '@/stores/search';
import { FinderMode, SearchMode } from '@/types/search';
import { Check, ChevronDown, Search, Settings2, X } from 'lucide-react';
import { useState } from 'react';

type SearchSettingsProps = {
  className?: string;
  mode: SearchMode;
  onModeChange: (mode: SearchMode) => void;
  finderMode: FinderMode;
  onFinderModeChange: (mode: FinderMode) => void;
};

export function SearchSettings({
  className,
  mode,
  onModeChange,
  finderMode,
  onFinderModeChange,
}: SearchSettingsProps) {
  const { config, setConfig } = useSearchStore();
  const { data: spaces, isLoading: spacesLoading } = clientApi.space.listAll.useQuery();
  const { data: materials, isLoading: materialsLoading } = clientApi.material.list.useQuery();
  const [spaceSearch, setSpaceSearch] = useState('');
  const [documentSearch, setDocumentSearch] = useState('');
  const [spaceOpen, setSpaceOpen] = useState(false);
  const [documentOpen, setDocumentOpen] = useState(false);

  // Use the filterType from the store to determine the active tab
  // This allows users to freely switch between tabs regardless of current selections
  const currentFilterType = config.filterType;

  const selectedSpaces = spaces?.filter((space) => config.spaceIds.includes(space.id)) ?? [];
  const selectedDocuments = materials?.filter((doc) => config.documentIds?.includes(doc.id)) ?? [];

  const handleSpaceSelect = (spaceId: string) => {
    if (spaceId === 'all') {
      setConfig({ spaceIds: [], documentIds: [], filterType: 'space' });
      setSpaceOpen(false);
      return;
    }

    const newSpaceIds = config.spaceIds.includes(spaceId)
      ? config.spaceIds.filter((id) => id !== spaceId)
      : [...config.spaceIds, spaceId];

    // When selecting spaces, clear document selections and ensure filterType is 'space'
    setConfig({ spaceIds: newSpaceIds, documentIds: [], filterType: 'space' });
  };

  const handleDocumentSelect = (documentId: string) => {
    if (documentId === 'all') {
      setConfig({ documentIds: [], spaceIds: [], filterType: 'document' });
      setDocumentOpen(false);
      return;
    }

    const newDocumentIds = config.documentIds?.includes(documentId)
      ? config.documentIds.filter((id) => id !== documentId)
      : [...(config.documentIds ?? []), documentId];

    // When selecting documents, clear space selections and ensure filterType is 'document'
    setConfig({ documentIds: newDocumentIds, spaceIds: [], filterType: 'document' });
  };

  const filteredSpaces = spaces?.filter((space) =>
    space.name.toLowerCase().includes(spaceSearch.toLowerCase())
  );

  const filteredDocuments = materials?.filter((doc) =>
    doc.fileName.toLowerCase().includes(documentSearch.toLowerCase())
  );

  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardContent className="space-y-6 p-6">
        <div className="mb-2 flex items-center gap-2">
          <Settings2 className="h-5 w-5 text-muted-foreground" />
          <h2 className="text-lg font-semibold">Search Settings</h2>
        </div>

        <div className="space-y-6">
          <div className="space-y-3">
            <Label className="text-sm font-medium">Search Mode</Label>
            <RadioGroup
              value={mode}
              onValueChange={(value) => onModeChange(value as SearchMode)}
              className="grid grid-cols-2 gap-4"
            >
              <div>
                <RadioGroupItem value="finder" id="finder" className="peer sr-only" />
                <Label
                  htmlFor="finder"
                  className="flex flex-col items-center justify-between rounded-xl border-2 border-muted bg-popover p-4 transition-colors hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <Search className="mb-2 h-5 w-5" />
                  <span className="text-sm font-medium">Finder</span>
                </Label>
              </div>

              <div>
                <RadioGroupItem value="chat" id="chat" className="peer sr-only" />
                <Label
                  htmlFor="chat"
                  className="flex flex-col items-center justify-between rounded-xl border-2 border-muted bg-popover p-4 transition-colors hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <svg
                    className="mb-2 h-5 w-5"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                  </svg>
                  <span className="text-sm font-medium">Chat</span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {mode === 'finder' && (
            <>
              <div className="space-y-3">
                <Label className="text-sm font-medium">Finder Mode</Label>
                <RadioGroup
                  value={finderMode}
                  onValueChange={(value) => {
                    onFinderModeChange(value as FinderMode);
                    setConfig({ finderMode: value as FinderMode });
                  }}
                  className="grid grid-cols-2 gap-4"
                >
                  <div>
                    <RadioGroupItem value="embedding" id="embedding" className="peer sr-only" />
                    <Label
                      htmlFor="embedding"
                      className="flex flex-col items-center justify-between rounded-xl border-2 border-muted bg-popover p-4 transition-colors hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <span className="text-sm font-medium">Semantic</span>
                      <span className="mt-1 text-xs text-muted-foreground">AI-powered search</span>
                    </Label>
                  </div>

                  <div>
                    <RadioGroupItem value="fulltext" id="fulltext" className="peer sr-only" />
                    <Label
                      htmlFor="fulltext"
                      className="flex flex-col items-center justify-between rounded-xl border-2 border-muted bg-popover p-4 transition-colors hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <span className="text-sm font-medium">Keyword</span>
                      <span className="mt-1 text-xs text-muted-foreground">Exact match search</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            </>
          )}

          <div className="space-y-3">
            <div className="rounded-lg bg-green-50 p-3 dark:bg-green-950/20">
              <div className="flex items-center gap-2">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-green-500">
                  <Check className="h-2.5 w-2.5 text-white" />
                </div>
                <Label className="text-sm font-medium text-green-900 dark:text-green-100">
                  Intelligent Search Results
                </Label>
              </div>
              <p className="mt-2 text-xs text-green-700 dark:text-green-300">
                Search results are automatically optimized based on your query. Our AI determines
                the best number of documents to show you.
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Filter Type</Label>
            </div>
            <Tabs
              value={currentFilterType}
              onValueChange={(value) => {
                const newFilterType = value as 'space' | 'document';
                // Update the filterType in the store and clear the selections of the other type
                // This ensures the two filter types are mutually exclusive
                if (newFilterType === 'space') {
                  // Switching to space filter - clear document selections and update filterType
                  setConfig({ filterType: 'space', documentIds: [] });
                } else {
                  // Switching to document filter - clear space selections and update filterType
                  setConfig({ filterType: 'document', spaceIds: [] });
                }
              }}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="document">Document Filter</TabsTrigger>
                <TabsTrigger value="space">Space Filter</TabsTrigger>
              </TabsList>
            </Tabs>

            {currentFilterType === 'space' ? (
              <div className="space-y-3">
                {spacesLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <>
                    <Popover open={spaceOpen} onOpenChange={setSpaceOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={spaceOpen}
                          className="w-full justify-between"
                        >
                          <span className="truncate">
                            {selectedSpaces.length > 0
                              ? `${selectedSpaces.length} spaces selected`
                              : 'All Spaces'}
                          </span>
                          <div className="flex items-center gap-1">
                            {selectedSpaces.length > 0 && (
                              <X
                                className="h-4 w-4 opacity-50 hover:opacity-100"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setConfig({ spaceIds: [], documentIds: [], filterType: 'space' });
                                }}
                              />
                            )}
                            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
                          </div>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="start">
                        <Command>
                          <CommandInput
                            placeholder="Search spaces..."
                            value={spaceSearch}
                            onValueChange={setSpaceSearch}
                          />
                          <CommandList>
                            <CommandEmpty>No spaces found.</CommandEmpty>
                            <CommandGroup>
                              <CommandItem
                                value="all"
                                onSelect={() => handleSpaceSelect('all')}
                                className="cursor-pointer"
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    selectedSpaces.length === 0 ? 'opacity-100' : 'opacity-0'
                                  )}
                                />
                                All Spaces
                              </CommandItem>
                              {filteredSpaces?.map((space) => (
                                <CommandItem
                                  key={space.id}
                                  value={space.id}
                                  onSelect={() => handleSpaceSelect(space.id)}
                                  className="cursor-pointer"
                                >
                                  <Check
                                    className={cn(
                                      'mr-2 h-4 w-4',
                                      config.spaceIds.includes(space.id)
                                        ? 'opacity-100'
                                        : 'opacity-0'
                                    )}
                                  />
                                  {space.name}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <div className="flex flex-wrap gap-2">
                      {selectedSpaces.map((space) => (
                        <Badge key={space.id} variant="secondary">
                          {space.name}
                          <X
                            className="ml-1 h-3 w-3 cursor-pointer"
                            onClick={() => handleSpaceSelect(space.id)}
                          />
                        </Badge>
                      ))}
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="space-y-3">
                {materialsLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <>
                    <Popover open={documentOpen} onOpenChange={setDocumentOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={documentOpen}
                          className="w-full justify-between"
                        >
                          <span className="truncate">
                            {selectedDocuments.length > 0
                              ? `${selectedDocuments.length} documents selected`
                              : 'All Documents'}
                          </span>
                          <div className="flex items-center gap-1">
                            {selectedDocuments.length > 0 && (
                              <X
                                className="h-4 w-4 opacity-50 hover:opacity-100"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setConfig({
                                    documentIds: [],
                                    spaceIds: [],
                                    filterType: 'document',
                                  });
                                }}
                              />
                            )}
                            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
                          </div>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="start">
                        <Command>
                          <CommandInput
                            placeholder="Search documents..."
                            value={documentSearch}
                            onValueChange={setDocumentSearch}
                          />
                          <CommandList>
                            <CommandEmpty>No documents found.</CommandEmpty>
                            <CommandGroup>
                              <CommandItem
                                value="all"
                                onSelect={() => handleDocumentSelect('all')}
                                className="cursor-pointer"
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    selectedDocuments.length === 0 ? 'opacity-100' : 'opacity-0'
                                  )}
                                />
                                All Documents
                              </CommandItem>
                              {filteredDocuments?.map((doc) => (
                                <CommandItem
                                  key={doc.id}
                                  value={doc.id}
                                  onSelect={() => handleDocumentSelect(doc.id)}
                                  className="cursor-pointer"
                                >
                                  <Check
                                    className={cn(
                                      'mr-2 h-4 w-4',
                                      config.documentIds?.includes(doc.id)
                                        ? 'opacity-100'
                                        : 'opacity-0'
                                    )}
                                  />
                                  {doc.fileName}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <div className="flex flex-wrap gap-2">
                      {selectedDocuments.map((doc) => (
                        <Badge key={doc.id} variant="secondary">
                          {doc.fileName}
                          <X
                            className="ml-1 h-3 w-3 cursor-pointer"
                            onClick={() => handleDocumentSelect(doc.id)}
                          />
                        </Badge>
                      ))}
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
