import { db } from '@/database/drizzle';
import { MaterialRepository } from '@/database/repository/material';
import { SpaceRepository } from '@/database/repository/space';
import { documentSpaces, spaces } from '@/database/schema';
import { TRPCError } from '@trpc/server';
import { eq, inArray } from 'drizzle-orm';
import { z } from 'zod';
import { protectedProcedure, router } from '../';

export const materialRouter = router({
  list: protectedProcedure.query(async ({ ctx }) => {
    // Get all documents for user (owned + accessed via spaces)
    const userDocs = await SpaceRepository.getAllDocumentsForUser(ctx.user.id);

    // For each document, filter out spaces that are not owned by the user and not shared
    const filteredDocs = await Promise.all(
      userDocs.map(async (doc) => {
        // Keep track of user-owned spaces
        const userOwnedSpaceIds = doc.documentSpaces
          .filter((ds) => ds.space.ownerId === ctx.user.id)
          .map((ds) => ds.space.id);

        // Get IDs of shared spaces for this document
        const sharedSpaces = await db
          .select({
            id: spaces.id,
          })
          .from(spaces)
          .innerJoin(documentSpaces, eq(documentSpaces.spaceId, spaces.id))
          .where(
            inArray(
              spaces.id,
              doc.documentSpaces.map((ds) => ds.space.id)
            )
          );

        const sharedSpaceIds = sharedSpaces.map((s) => s.id);

        // Filter document spaces to only include:
        // 1. Spaces owned by the user
        // 2. Spaces that are shared
        return {
          ...doc,
          documentSpaces: doc.documentSpaces.filter(
            (ds) =>
              userOwnedSpaceIds.includes(ds.space.id) ||
              (sharedSpaceIds.includes(ds.space.id) && ds.space.isShared)
          ),
        };
      })
    );

    // Only include documents that still have at least one accessible space
    // or that are owned by the user directly
    return filteredDocs.filter(
      (doc) => doc.uploadedBy === ctx.user.id || doc.documentSpaces.length > 0
    );
  }),

  updateFilename: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        fileName: z.string().min(1, 'Filename cannot be empty'),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const material = await MaterialRepository.getMaterialById(input.id);
      if (!material) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Material not found' });
      }

      // Check ownership
      if (material[0].uploadedBy !== ctx.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to edit this material',
        });
      }

      return MaterialRepository.updateMaterialFilename(input.id, input.fileName);
    }),

  delete: protectedProcedure.input(z.string()).mutation(async ({ ctx, input }) => {
    const material = await MaterialRepository.getMaterialById(input);
    if (!material) {
      throw new TRPCError({ code: 'NOT_FOUND', message: 'Material not found' });
    }

    // Check ownership
    if (material[0].uploadedBy !== ctx.user.id) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to delete this material',
      });
    }

    return MaterialRepository.deleteMaterial(material[0].id, material[0].s3Key);
  }),

  getSummary: protectedProcedure.input(z.string()).query(async ({ ctx, input }) => {
    const material = await MaterialRepository.getMaterialById(input);
    if (!material || material.length === 0) {
      throw new TRPCError({ code: 'NOT_FOUND', message: 'Material not found' });
    }

    const doc = material[0];

    // Check if user has access to this document
    // Either they own it, or it's in a space they have access to
    if (doc.uploadedBy !== ctx.user.id) {
      // Check if document is in any accessible spaces
      const userSpaces = await SpaceRepository.getAllSpacesForUser(ctx.user.id);
      const userSpaceIds = userSpaces.map((s) => s.id);

      const docSpaces = await db
        .select({ spaceId: documentSpaces.spaceId })
        .from(documentSpaces)
        .where(eq(documentSpaces.documentId, input));

      const hasAccess = docSpaces.some((ds) => userSpaceIds.includes(ds.spaceId));

      if (!hasAccess) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to access this material',
        });
      }
    }

    return {
      id: doc.id,
      fileName: doc.fileName,
      summary: doc.summary,
      key: doc.s3Key,
      fileType: doc.fileType,
    };
  }),
});
